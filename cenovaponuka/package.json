{"name": "cenova-ponuka-electron", "version": "1.0.0", "description": "Electron aplikácia pre generovanie cenových ponúk pre služby starostlivosti o hrobové miesta", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-mac": "electron-builder --mac", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "sk.ehroby.cenova-ponuka", "productName": "Cenová <PERSON> - ehroby.sk", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.business", "target": "dir", "identity": null}, "win": {"target": "dir"}, "linux": {"target": "dir"}}, "keywords": ["electron", "pdf", "cenova-ponuka", "<PERSON><PERSON><PERSON>", "hrobove-miesta"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT"}