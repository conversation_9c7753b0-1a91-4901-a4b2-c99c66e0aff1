{"version": 3, "file": "disposable.js", "sourceRoot": "", "sources": ["../../../../src/util/disposable.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AA0BF,MAAc,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC;AAC7C,MAAc,CAAC,YAAY,KAAK,MAAM,CAAC,cAAc,CAAC,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAA0B,MAAM,CAAC,OAAO,CAAC;AAEnE;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAC7B,MAAM,CAAC,YAAY,CAAC;AAEtB;;GAEG;AACH,MAAM,OAAO,eAAe;IAC1B,SAAS,GAAG,KAAK,CAAC;IAClB,MAAM,GAAiB,EAAE,CAAC;IAE1B;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7C,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAA0C,KAAQ;QACnD,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAI,KAAQ,EAAE,SAA6B;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,aAAa,CAAC;gBACb,SAAS,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC;SACF,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAqB;QACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,aAAa,CAAC;gBACb,SAAS,EAAE,CAAC;YACd,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,cAAc,CAAC,2CAA2C,CAAC,CAAC,CAAC,SAAS;QAClF,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,eAAe,EAAE,CAAC,CAAC,WAAW;QAChD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;IAEtB,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,iBAAiB,CAAC;CACnD;AAED;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAC/B,SAAS,GAAG,KAAK,CAAC;IAClB,MAAM,GAAsB,EAAE,CAAC;IAE/B;;OAEG;IACH,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7C,MAAM,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAA+C,KAAQ;QACxD,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAI,KAAQ,EAAE,SAAsC;QACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,kBAAkB,CAAC;gBAClB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;SACF,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAA8B;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,CAAC,kBAAkB,CAAC;gBAClB,OAAO,SAAS,EAAE,CAAC;YACrB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,IAAI;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,cAAc,CAAC,2CAA2C,CAAC,CAAC,CAAC,SAAS;QAClF,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,oBAAoB,EAAE,CAAC,CAAC,WAAW;QACrD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;IAE3B,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,sBAAsB,CAAC;CACxD"}